---,name,time,conditions,defaultRole,zones,lines,syncParams
ChipandChase,"ChipandChase","-1.966667","(originTarget=SETPLAY_RUCK_CENTER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Kicker"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(CATCHPASS,KICKCHIP),syncPoints=(""NULL"",""KickerHasBall""),originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=KICKING,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Passer"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_SCRUM_HALF,numPlayers=1)),actionList=(PASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Catcher"",priority=108.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.181090,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.977180,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(CATCHKICK),syncPoints=(""KickerHasBall""),originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","","((key=""KickerHasBall"",numPlayers=2))"
Conversion:Attack,"Conversion:Attack","-2.200000","(originTarget=RESTART_CENTERED,attacking=ATTACK,setPlay=True,gamePhase=CONVERSION,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","TUTORIAL","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.963591,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.295867,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.533520,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.430109,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.561872,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.427944,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.659960,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.608290,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.290940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.873460,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.432859,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=16.734739,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.887060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.353439,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Shooter"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.037201,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.044710,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SHOOT_FOR_GOAL,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-17.399191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=27.861349,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.437246,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.767770,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.889960,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.893522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.661280,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.742399,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=11.622310,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.469816,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.817676,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.209438,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-12.406200,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.608801,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.597600,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.998000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Conversion:Defence,"Conversion:Defence","-2.200000","(originTarget=PENALTY_SHOT_DEFENSIVE_ORIGIN,attacking=DEFENCE,setPlay=True,gamePhase=CONVERSION,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-26.190479,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=53.003471,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-23.845169,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=57.853870,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-13.153220,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.912209,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.820314,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.817490,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.020514,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.723370,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.294088,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=53.273170,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=12.450320,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.814030,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=23.400551,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.851608,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-18.899191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=55.250912,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-7.770579,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=55.032619,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.931110,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.213870,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=55.027920,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=54.942360,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
CrossKick,"CrossKick","-1.966667","(originTarget=SETPLAY_RUCK_CENTER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Kicker"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(CATCHPASS,KICKPUNT),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=KICKING,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Passer"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_SCRUM_HALF,numPlayers=1)),actionList=(PASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Catcher"",priority=108.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-30.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.181090,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.977180,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=XDIST_FROM_BALL,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(CATCHKICK),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Cutscene,"Cutscene","0.000000","(originTarget=TRACKBALL,attacking=BOTH,setPlay=True,gamePhase=CUTSCENE,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=NONE)","NULL_ROLE","","",""
DropOut:Attack,"DropOut:Attack","-2.200000","(originTarget=RESTART_CENTERED,attacking=ATTACK,setPlay=True,gamePhase=DROPOUT,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=NONE)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-22.905680,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.686367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-11.299780,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.686367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.459030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.774754,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-4.271651,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.204272,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.290940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.861518,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.341749,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.625723,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.887060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=6.113735,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=18.819210,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.523414,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=23.769510,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.251235,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Kicker"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.098316,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.467493,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFFKICKER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.662190,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.893522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.892827,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=14.583110,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.706586,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
DropOut:Defence,"DropOut:Defence","-2.200000","(originTarget=RESTART_POSITION,attacking=DEFENCE,setPlay=True,gamePhase=DROPOUT,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=50.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=34.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-34.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.209999,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=54.209999,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=1.210000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=54.428989,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-1.064590,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=62.742470,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.814051,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.128691,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-14.855244,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=65.011703,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.530060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=50.771080,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-30.445045,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=59.723000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.765329,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.081051,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
FreeKick:Attack,"FreeKick:Attack","-2.200000","(originTarget=RESTART_POSITION,attacking=ATTACK,setPlay=False,gamePhase=TEN_M_KICK,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""P1"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=50.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=KICKOFFKICKER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=9.907794,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.510704,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.948263,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.470614,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
FreeKick:Defence,"FreeKick:Defence","-2.200000","(originTarget=RESTART_POSITION,attacking=DEFENCE,setPlay=False,gamePhase=TEN_M_KICK,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=20.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=42.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
FreeBall:Attack,"FreeBall:Attack","-1.966667","(originTarget=TRACKBALL,attacking=ATTACK,setPlay=False,gamePhase=FREEBALL,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=50.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FULLBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=TRACKBALL_VEL_Z,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""GetTheBall"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=GETTHEBALL,numPlayers=4)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
FreeBall:Defence,"FreeBall:Defence","-1.966667","(originTarget=TRACKBALL,attacking=DEFENCE,setPlay=False,gamePhase=FREEBALL,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""P1"",priority=50.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=21.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FULLBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""GetTheBall"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=GETTHEBALL,numPlayers=4)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
FullTime,"FullTime","0.000000","(originTarget=ORIGIN,attacking=BOTH,setPlay=True,gamePhase=FULLTIME,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""Zone:0"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.678140,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=26.747610,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=FORMATION,numPlayers=13)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
HalfTime,"HalfTime","0.000000","(originTarget=ORIGIN,attacking=BOTH,setPlay=True,gamePhase=HALFTIME,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""Zone:0"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.678140,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=26.747610,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=FORMATION,numPlayers=13)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
KickOff:Attack,"KickOff:Attack","-2.200000","(originTarget=RESTART_POSITION,attacking=ATTACK,setPlay=True,gamePhase=KICKOFF,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","KICKOFF_CHASER","((name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=12.671140,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.610494,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.562410,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.678920,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=6.241083,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=28.164770,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.780175,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=24.540739,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.080814,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=19.640450,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.653710,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=7.712990,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.290726,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.562410,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-26.221201,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.439552,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.981806,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-7.672487,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.789801,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.031517,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-16.049990,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.458306,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.910250,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Kicker"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.489117,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.643096,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.900727,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFFKICKER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=2.144842,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.718876,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.900727,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.719311,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.351349,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.996000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.672487,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.789801,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.031517,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-6.705882,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.030693,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.030625,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
KickOff:Defence,"KickOff:Defence","-2.200000","(originTarget=RESTART_POSITION,attacking=DEFENCE,setPlay=True,gamePhase=KICKOFF,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=50.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=34.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-34.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.209999,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=54.209999,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=1.210000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=54.428989,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-1.064590,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=62.742470,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.814051,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.128691,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-14.855244,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=65.011703,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.530060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=50.771080,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-30.445045,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=59.723000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.765329,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.081051,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Penalty:Attack,"Penalty:Attack","0.000000","(originTarget=RESTART_POSITION,attacking=ATTACK,setPlay=False,gamePhase=PENALTY,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=40.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=50.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=TAP_RESTART,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_POSITION,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-12.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Penalty:Defence,"Penalty:Defence","0.000000","(originTarget=RESTART_POSITION,attacking=DEFENCE,setPlay=False,gamePhase=PENALTY,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=35.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=XDIST_FROM_BALL,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FULLBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=16.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Pendulum:Defence,"Pendulum:Defence","-1.966667","(originTarget=TRACKBALL,attacking=DEFENCE,setPlay=False,gamePhase=STANDARDPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Forwards"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.212718,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.724367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=12.093210,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.394949,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=MARKDEFEND,numPlayers=1),(role=MARKDEFEND,numPlayers=5)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=5.000000,type=STANDARD,movementType=PENDULUM,mvParam1=0.500000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=26.830009,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=XDIST_FROM_BALL,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.206944,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.043468,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FULLBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Number 9"",priority=5.000000,type=STANDARD,movementType=PENDULUM,mvParam1=0.300000,mvParam2=3.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.826540,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""leftwing"",priority=5.000000,type=STANDARD,movementType=PENDULUM,mvParam1=0.400000,mvParam2=0.250000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=21.068180,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=LEFT_WING_DEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""rightwing"",priority=5.000000,type=STANDARD,movementType=PENDULUM,mvParam1=0.400000,mvParam2=0.250000,x=(points=((x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=21.347509,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=RIGHT_WING_DEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""R-Backline"",priority=2.000000,spacing=5.000000,minSpacing=5.000000,urgencyMode=XDIST_FROM_BALL,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=16.506290,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.177087,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.524680,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.524680,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.177087,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=16.506290,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKS,roles=((role=MARKDEFEND,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
PlayTheBall:Attack,"PlayTheBall:Attack","0.000000","(originTarget=RESTART_POSITION,attacking=ATTACK,setPlay=False,gamePhase=PLAY_THE_BALL,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=READY_ENGAGE)","FORMATION","((name=""Ball Holder"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.334819,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.558984,g0=0.000000,g1=0.000000,pointType=0))),playerMask=BALL_HOLDER,roles=((role=PLAY_THE_BALL,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P1"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=40.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-12.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=OPENSIDE_BIAS,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.500000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=PLAYERPOSITION,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ball Receiver"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.700000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=80.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.334819,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.558984,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=PLAY_THE_BALL_RECEIVER,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
PlayTheBall:Defence,"PlayTheBall:Defence","0.000000","(originTarget=RESTART_POSITION,attacking=DEFENCE,setPlay=False,gamePhase=PLAY_THE_BALL,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=READY_ENGAGE)","FORMATION","((name=""Tackler standby"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.700000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=80.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=PLAY_THE_BALL_DEFENDER,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Tackler standby"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.700000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=80.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=PLAY_THE_BALL_SECOND_DEFENDER,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P1"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=40.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=101.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=RIGHT_WING_DEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=101.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=LEFT_WING_DEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-12.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Pre-dropout:Attack,"Pre-dropout:Attack","-2.200000","(originTarget=RESTART_CENTERED,attacking=ATTACK,setPlay=True,gamePhase=PRE_DROPOUT,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=NONE)","REACTION_CUTSCENE","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-22.905680,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.686367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-11.299780,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.686367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.459030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.774754,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-4.271651,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.204272,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.290940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.861518,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.341749,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.625723,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.887060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=6.113735,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=18.819210,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.523414,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=23.769510,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.251235,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Kicker"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.098316,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.467493,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.662190,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.893522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.892827,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=14.583110,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.706586,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=KICKOFF_CHASER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
pre-dropout:Defence,"pre-dropout:Defence","-2.200000","(originTarget=RESTART_CENTERED,attacking=DEFENCE,setPlay=True,gamePhase=PRE_DROPOUT,warpToPositions=True,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-23.555981,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.613718,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.582844,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.613718,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=17.607630,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.395164,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-7.016231,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.773157,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=RESTART_POSITION,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=30.321360,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=13.885940,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.654659,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.695700,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.021532,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.923057,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.043261,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=RESTART_POSITION,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.545584,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.303040,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-18.899191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=29.707170,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=POD,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=6.344404,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.788909,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.721228,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=RESTART_POSITION,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=26.848459,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.393522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.921329,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.835779,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
PreKickOff:Attack,"PreKickOff:Attack","-2.200000","(originTarget=ORIGIN,attacking=ATTACK,setPlay=True,gamePhase=PRE_KICKOFF,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=11.102800,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.145103,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.562410,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=22.475019,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.775692,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=28.164770,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.314784,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=24.540739,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.615423,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=19.640450,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.188319,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.491188,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.825334,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.562410,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.339580,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.974161,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.981806,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=1.938151,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.057841,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.232201,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.595652,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.910250,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-12.110330,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.643096,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.900727,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.603307,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.562410,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.135011,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-4.520567,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.164629,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.900727,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.947081,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.619389,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.996000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
PreKickOff:Defence,"PreKickOff:Defence","-2.200000","(originTarget=ORIGIN,attacking=DEFENCE,setPlay=True,gamePhase=PRE_KICKOFF,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","UNUSED","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.963169,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.098700,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.533520,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.098700,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.661922,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.187090,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.659960,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.020620,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.290940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.285789,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.432859,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=28.147070,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.887060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=28.765770,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-9.296133,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=28.623711,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-18.899191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=39.107010,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-7.770579,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=39.427662,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=36.248310,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.393522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=40.321171,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=40.235619,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Pre_penaltykick:Attack,"Pre_penaltykick:Attack","-2.200000","(originTarget=RESTART_CENTERED,attacking=ATTACK,setPlay=True,gamePhase=PRE_KICKFORPOINTS,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","TUTORIAL","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.963169,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.087067,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.533520,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.087067,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.561872,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.175454,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.659960,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.608290,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.290940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.873460,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.432859,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=16.734739,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.887060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.353439,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Pre-Shooter"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.037201,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.544918,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-17.399191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=27.861349,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.437246,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.767770,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.889960,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.893522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.661280,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.742399,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Pre_PenaltyKick:Defence,"Pre_PenaltyKick:Defence","-2.200000","(originTarget=ORIGIN,attacking=DEFENCE,setPlay=True,gamePhase=PRE_KICKFORPOINTS,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-26.190479,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=53.003471,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-23.845169,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=57.853870,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-13.153220,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.912209,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.820314,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.817490,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.020514,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.723370,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=8.294088,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=53.273170,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=12.450320,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.814030,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=23.400551,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.851608,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-18.899191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=55.250912,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-7.770579,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=55.032619,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=52.931110,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.213870,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=55.027920,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=54.942360,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
QuickTapPenalty:Attack,"QuickTapPenalty:Attack","0.000000","(originTarget=RESTART_POSITION,attacking=ATTACK,setPlay=False,gamePhase=QUICK_TAP_PENALTY,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Tap restart"",priority=50.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=1.000000,maxUrgency=1.000000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.334819,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.558984,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=TAP_RESTART,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_POSITION,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Wait group forwards"",priority=2.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-20.050911,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.546770,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.100000,maxUrgency=0.100000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=30.246550,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=4.265677,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDSTHENBACKS,roles=((role=FORMATION,numPlayers=6)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Wait group backs"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=13.234020,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.433304,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.100000,maxUrgency=0.100000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=33.133640,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.203670,g0=0.000000,g1=0.000000,pointType=0))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=6)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
QuickTapPenalty:Defence,"QuickTapPenalty:Defence","0.000000","(originTarget=RESTART_POSITION,attacking=DEFENCE,setPlay=False,gamePhase=QUICK_TAP_PENALTY,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=0.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Wait group forwards"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-18.355820,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.210360,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.200000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=29.385450,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=11.608100,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDSTHENBACKS,roles=((role=MARKDEFEND,numPlayers=6)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=10.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.524981,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=40.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.100000,maxUrgency=0.200000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Wait group backs"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=11.223940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.510020,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.100000,maxUrgency=0.200000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=33.383209,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.323359,g0=0.000000,g1=0.000000,pointType=0))),playerMask=BACKSTHENFORWARDS,roles=((role=MARKDEFEND,numPlayers=6)),actionList=,syncPoints=,originTarget=RESTART_CENTERED,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Wing1"",priority=9.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-32.034988,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.100000,maxUrgency=0.100000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=WING,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Wing2"",priority=9.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=24.786200,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.100000,maxUrgency=0.100000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=WING,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
ReactionCutscene,"ReactionCutscene","0.000000","(originTarget=RESTART_POSITION,attacking=BOTH,setPlay=True,gamePhase=REACTION_CUTSCENE,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","REACTION_CUTSCENE","((name=""MillingArea"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=PLAYERPOSITION,width=(points=((x=0.000000,y=40.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=REACTION_CUTSCENE,numPlayers=13)),actionList=,syncPoints=,originTarget=RESTART_POSITION,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Scrum:Attack,"Scrum:Attack","-1.966667","(originTarget=SCRUM_ORIGIN,attacking=ATTACK,setPlay=True,gamePhase=SCRUM,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Forwards"",priority=95.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.212718,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.724367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=12.093210,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.394949,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDSTHENBACKS,roles=((role=SCRUM,numPlayers=6)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalf"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.500000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.499999,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SCRUM_HALFBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-2.817718,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.860256,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.165176,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.456022,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=SCRUM_ORIGIN,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=90.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.700000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.700001,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Backs"",priority=1.000000,spacing=5.000000,minSpacing=5.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=OPENBLIND,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-42.423130,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=24.738750,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.472277,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.730013,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.015845,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.865269,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=42.521240,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=24.412201,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=FAN_OPENBLIND))",""
Scrum:Defence,"Scrum:Defence","-1.966667","(originTarget=SCRUM_ORIGIN,attacking=DEFENCE,setPlay=True,gamePhase=SCRUM,warpToPositions=True,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""Forwards"",priority=95.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.212718,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.724367,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=12.093210,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.394949,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDSTHENBACKS,roles=((role=SCRUM,numPlayers=6)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalf"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.202600,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=6.903450,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.006000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.006000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SCRUM_HALFBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=90.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=19.299801,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.700000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.700001,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Line 1 (2)"",priority=1.000000,spacing=5.000000,minSpacing=5.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=OPENBLIND,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-39.770050,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.285490,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-3.809242,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.342881,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.190758,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.915154,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=39.079529,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.142320,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=FAN_OPENBLIND))",""
Simulation,"Simulation","0.000000","(originTarget=ORIGIN,attacking=BOTH,setPlay=True,gamePhase=SIMULATION,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""Zone:0"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.678140,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=26.747610,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=FORMATION,numPlayers=13)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Standard:Attack,"Standard:Attack","-1.966667","(originTarget=TRACKBALL_VEL_Z,attacking=ATTACK,setPlay=False,gamePhase=STANDARDPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=NONE)","FORMATION","((name=""BallHolder"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.500000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.499999,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=BALLHOLDER,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.107420,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.516625,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.710938,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P1"",priority=50.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=27.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=48.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.700000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=20.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=20.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-32.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=20.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=20.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=32.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=9.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Standard:Defence,"Standard:Defence","-1.966667","(originTarget=TRACKBALL,attacking=DEFENCE,setPlay=False,gamePhase=STANDARDPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-65.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""P1"",priority=5.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=35.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=XDIST_FROM_BALL,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FULLBACK,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),,(name=""P3"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P3,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),,(name=""P6"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-32.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=((role=LEFT_WING_DEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=32.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=((role=RIGHT_WING_DEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=49.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=((role=MARKDEFEND,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""GetTheBall"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=GETTHEBALL,numPlayers=0)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
TryCutscene:Attack,"TryCutscene:Attack","-2.200000","(originTarget=RESTART_CENTERED,attacking=ATTACK,setPlay=False,gamePhase=TRY_CUTSCENE,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","TUTORIAL","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.963169,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.087067,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.533520,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.087067,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.561872,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.175454,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.659960,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.608290,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.290940,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.873460,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-29.432859,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=16.734739,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.887060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.353439,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.037201,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.044710,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-17.399191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=27.861349,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.437246,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=15.767770,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-31.536030,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.889960,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=4.893522,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.661280,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=9.522682,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=16.674219,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.742399,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.400000,maxUrgency=0.400000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
TryCutscene:Defence,"TryCutscene:Defence","-2.200000","(originTarget=ORIGIN,attacking=DEFENCE,setPlay=False,gamePhase=TRY_CUTSCENE,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""P1"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.140640,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.840778,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P2"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-23.370090,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.845379,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P2,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P3"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-13.913350,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.369450,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P4"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-4.960504,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.844830,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P4,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P5"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=2.924976,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.845730,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P5,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P6"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=10.574470,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.730412,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P6,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P7"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=14.825710,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.556320,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P7,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P8"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=24.350710,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.213840,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P8,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P9"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-18.899191,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.712818,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.903461,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P10"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-8.815753,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.779591,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.903461,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P10,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P11"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-32.106121,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.483379,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P11,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P12"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=6.454902,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.968399,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.903461,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P12,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""P13"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=19.475439,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=60.391441,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=4.598744,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P13,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
TryReaction:Attack,"TryReaction:Attack","-2.200000","(originTarget=ORIGIN,attacking=ATTACK,setPlay=False,gamePhase=TRY_REACTION,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","FORMATION","((name=""All"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-31.897160,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=36.478439,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=26.443880,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=TRY_REACTION,numPlayers=13)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=11.622310,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-56.897160,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.817676,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.209438,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
TryReaction:Defence,"TryReaction:Defence","-2.200000","(originTarget=ORIGIN,attacking=DEFENCE,setPlay=False,gamePhase=TRY_REACTION,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=AMBIENT_STOPPAGE)","TRY_REACTION","((name=""All"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-26.190479,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=34.947361,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=4.272108,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.226761,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=TRY_REACTION,numPlayers=13)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","",""
Tutorial,"Tutorial","0.000000","(originTarget=TRACKBALL,attacking=BOTH,setPlay=False,gamePhase=TUTORIALS,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=NONE)","FORMATION","((name=""Ball Holder"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.974845,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.489182,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=BALLHOLDER,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Line:0"",priority=1.000000,spacing=5.000000,minSpacing=5.000000,urgencyMode=XDIST_FROM_BALL,minUrgency=0.600000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=NORMALIZED,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-6.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=6.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=ALL,roles=((role=FORMATION,numPlayers=5)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
Tutorial:Offload,"Tutorial:Offload","0.000000","(originTarget=TRACKBALL,attacking=BOTH,setPlay=False,gamePhase=TUTORIALS,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=NONE)","TUTORIAL","((name=""Ball Holder"",priority=1.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=PLAYERPOSITION,width=(points=((x=0.000000,y=3.974845,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.489182,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=BALLHOLDER,numPlayers=1)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=FORWARDS,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Line:0"",priority=1.000000,spacing=4.000000,minSpacing=2.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=4.000000,maxUrgencyDistX=4.000000,type=MINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-3.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-1.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-2.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-1.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-1.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=2.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-1.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-1.000000,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=ALL,roles=((role=FORMATION,numPlayers=5)),originTarget=INHERIT,groupStrategy=FORWARDS,playerSortStrategy=RANDOM))",""
Tutorial:Defence,"Tutorial:Defence","0.000000","(originTarget=TRACKBALL,attacking=DEFENCE,setPlay=False,gamePhase=TUTORIALS,warpToPositions=False,xMirror=True,xMin=-35.000000,xMax=35.000000,zMin=-50.000000,zMax=50.000000,defaultIdleGroup=NONE)","FORMATION","","((name=""Line:0"",priority=1.000000,spacing=5.000000,minSpacing=5.000000,urgencyMode=XDIST_FROM_BALL,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=MINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-9.644160,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.178299,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-0.021261,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=-0.566244,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=9.459159,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.411504,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=ALL,roles=((role=MARKDEFEND,numPlayers=5)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
TwoPod:Attack,"TwoPod:Attack","-1.966667","(originTarget=TRACKBALL,attacking=ATTACK,setPlay=False,gamePhase=STANDARDPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","UNUSED","((name=""Forwards:Pod2"",priority=1.000000,type=STANDARD,movementType=POD,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=2.273623,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.309959,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=4.566466,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=4.566466,g0=0.000000,g1=0.000000,pointType=0))),playerMask=TWOPOD_2,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""BallHolder"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=3.500000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=3.499999,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=BALLHOLDER,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Ref"",priority=1.000000,type=STANDARD,movementType=REFEREE,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.107420,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.516625,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.710938,g0=0.000000,g1=0.000000,pointType=0))),playerMask=REFEREE,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=3.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.238750,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.583790,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=2.647484,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=2.647483,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P1,roles=((role=FORMATION,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards:Pod1"",priority=1.000000,type=STANDARD,movementType=POD,mvParam1=0.750000,mvParam2=0.170000,x=(points=((x=0.000000,y=-2.229042,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.165243,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=RANDOM,width=(points=((x=0.000000,y=4.388143,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=4.388143,g0=0.000000,g1=0.000000,pointType=0))),playerMask=TWOPOD_1,roles=((role=FORMATION,numPlayers=4)),actionList=,syncPoints=,originTarget=INHERIT,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""R-Backline"",priority=2.000000,spacing=5.000000,minSpacing=5.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-47.137680,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.096720,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-7.099704,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.091004,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=9.760020,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=1.782089,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=45.987640,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.401890,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKS,roles=((role=FORMATION,numPlayers=5)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
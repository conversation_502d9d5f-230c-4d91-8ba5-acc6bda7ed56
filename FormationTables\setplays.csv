---,name,time,conditions,defaultRole,zones,lines,syncParams
Pocket,"Pocket","-1.966667","(originTarget=SETPLAY_RUCK_CENTER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""LeftWing"",priority=102.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-7.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_SCRUM_HALF,numPlayers=1)),actionList=(PASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(CATCHPASS,DECISION),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000),(category=KICKING,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=False,targettedAction=KICKGOAL,isFlipped=False),(name=""InsideCenter"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""OutsideCenter"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-22.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-28.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=22.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""RightWing"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-40.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-40.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Breakdown Support"",priority=50.000000,spacing=3.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-9.204600,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=),z=(points=)),(x=(points=((x=0.000000,y=8.504250,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.301150,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=ALL,roles=((role=FORMATION,numPlayers=0)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Deep"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.600000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.300000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.600000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=4.300000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=0)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Flat"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.356480,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.666500,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-10.542850,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.168058,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=10.291980,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.340222,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.099060,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=8.773623,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=0)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
Dummy Skip Pass,"Dummy Skip Pass","-1.966667","(originTarget=SETPLAY_RUCK_CENTER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""LeftWing"",priority=102.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-7.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_SCRUM_HALF,numPlayers=1)),actionList=(NOTHING,PASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-11.500000,g0=0.000000,g1=0.000000,pointType=0),(x=3.000000,y=-14.000000,g0=0.000000,g1=0.000000,pointType=0),(x=4.000000,y=-29.450001,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=7.500000,g0=0.000000,g1=0.000000,pointType=0),(x=3.000000,y=7.500000,g0=0.000000,g1=0.000000,pointType=0),(x=4.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHPASS,DUMMYPASS,NOTHING,DECISION),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000)),passOrKickTarget=4.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""InsideCenter"",priority=108.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=19.500000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=3.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,BAITPASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=3.000000,stayBehindSelectedPlayer=True,targettedAction=PASS,isFlipped=False),(name=""OutsideCenter"",priority=107.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.500000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-25.500000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=29.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=True,targettedAction=PASS,isFlipped=False),(name=""Fullback"",priority=106.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=29.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=2.000000,stayBehindSelectedPlayer=True,targettedAction=PASS,isFlipped=False),(name=""RightWing"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-35.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-40.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-45.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=31.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Breakdown Support"",priority=50.000000,spacing=3.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-9.204600,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=),z=(points=)),(x=(points=((x=0.000000,y=8.504250,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.301150,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=0)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))","((key=""Empty"",numPlayers=1),(key=""WaitBeforeRunning"",numPlayers=2))"
Classic,"Classic","-1.966667","(originTarget=SETPLAY_RUCK_CENTER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""LeftWing"",priority=108.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=6.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-4.500000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-9.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=False,targettedAction=PASSVIA,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_SCRUM_HALF,numPlayers=1)),actionList=(PASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-4.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=6.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHPASS,DECISION),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""InsideCenter"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-11.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""OutsideCenter"",priority=107.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=22.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,DECISION),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=3.000000,stayBehindSelectedPlayer=False,targettedAction=PASS,isFlipped=False),(name=""Fullback"",priority=106.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-22.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-9.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHKICK),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=4.000000,stayBehindSelectedPlayer=False,targettedAction=KICKCHIP,isFlipped=False),(name=""FullbackGTB"",priority=99.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=SETPLAYGTB,roles=((role=GETTHEBALL,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""RightWing"",priority=105.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-35.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-38.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=34.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=2.000000,stayBehindSelectedPlayer=True,targettedAction=PASSVIA,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Breakdown Support"",priority=50.000000,spacing=3.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
Cross Kick and Wing,"Cross Kick and Wing","-1.966667","(originTarget=SETPLAY_RUCK_CENTER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","FORMATION","((name=""LeftWing"",priority=110.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=(""FirstRunnerBall""),originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=False,targettedAction=PASS,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_SCRUM_HALF,numPlayers=1)),actionList=(PASS),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-9.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=9.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHPASS,DECISION),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000),(category=KICKING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""InsideCenter"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""OutsideCenter"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-22.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-28.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""RightWing"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-50.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-50.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=3.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHKICK),syncPoints=,originTarget=SETPLAY_RUCK_CENTER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=2.000000,stayBehindSelectedPlayer=False,targettedAction=KICKPUNT,isFlipped=False),(name=""RightWingGetTheBall"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.000000,maxUrgency=0.000000,minUrgencyDistX=0.000000,maxUrgencyDistX=0.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=),height=(points=),playerMask=SETPLAYGTB,roles=((role=GETTHEBALL,numPlayers=1)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Breakdown Support"",priority=50.000000,spacing=3.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-9.204600,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=),z=(points=)),(x=(points=((x=0.000000,y=8.504250,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.301150,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=0)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
Classic_PTB,"Classic PTB","-1.966667","(originTarget=SETPLAY_PTB_RECEIVER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","SETPLAY_PTB_RECEIVER","((name=""LeftWing"",priority=108.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=6.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-4.500000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-9.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=23.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=False,targettedAction=PASSVIA,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_PTB_RECEIVER,numPlayers=1)),actionList=(PASS,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-4.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=6.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHPASS,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""InsideCenter"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-11.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""OutsideCenter"",priority=107.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=22.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=8.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=3.000000,stayBehindSelectedPlayer=False,targettedAction=PASS,isFlipped=False),(name=""Fullback"",priority=106.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-22.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-9.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHKICK),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=4.000000,stayBehindSelectedPlayer=False,targettedAction=KICKCHIP,isFlipped=False),(name=""FullbackGTB"",priority=99.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-24.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=SETPLAYGTB,roles=((role=GETTHEBALL,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""RightWing"",priority=105.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-35.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-38.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=34.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=2.000000,stayBehindSelectedPlayer=True,targettedAction=PASSVIA,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Breakdown Support"",priority=50.000000,spacing=5.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""
Dummy_Skip_Pass_PTB,"Dummy Skip Pass PTB","-1.966667","(originTarget=SETPLAY_PTB_RECEIVER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","SETPLAY_PTB_RECEIVER","((name=""LeftWing"",priority=102.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-7.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=10.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_PTB_RECEIVER,numPlayers=1)),actionList=(PASS,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-11.500000,g0=0.000000,g1=0.000000,pointType=0),(x=3.000000,y=-14.000000,g0=0.000000,g1=0.000000,pointType=0),(x=4.000000,y=-29.450001,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=7.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=7.500000,g0=0.000000,g1=0.000000,pointType=0),(x=3.000000,y=7.500000,g0=0.000000,g1=0.000000,pointType=0),(x=4.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHPASS,DUMMYPASS,NOTHING,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000)),passOrKickTarget=4.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""InsideCenter"",priority=108.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=19.500000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=3.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,BAITPASS),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=3.000000,stayBehindSelectedPlayer=True,targettedAction=PASS,isFlipped=False),(name=""OutsideCenter"",priority=107.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-19.500000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-25.500000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=29.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=True,targettedAction=PASS,isFlipped=False),(name=""Fullback"",priority=106.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-25.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-30.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=29.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=2.000000,stayBehindSelectedPlayer=True,targettedAction=PASS,isFlipped=False),(name=""RightWing"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-35.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=-40.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-45.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=31.000000,g0=0.000000,g1=0.000000,pointType=0),(x=0.000000,y=25.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Breakdown Support"",priority=50.000000,spacing=3.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-9.204600,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=),z=(points=)),(x=(points=((x=0.000000,y=8.504250,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.301150,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=0)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))","((key=""Empty"",numPlayers=1),(key=""WaitBeforeRunning"",numPlayers=2))"
Cross_Kick_and_Wing_PTB,"Cross Kick and Wing PTB","-1.966667","(originTarget=SETPLAY_PTB_RECEIVER,attacking=ATTACK,setPlay=False,gamePhase=SETPLAY,warpToPositions=False,xMirror=False,xMin=-35.000000,xMax=35.000000,zMin=-40.000000,zMax=65.000000,defaultIdleGroup=AMBIENT_IN_PLAY)","SETPLAY_PTB_RECEIVER","((name=""LeftWing"",priority=110.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-15.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=12.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=2.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=(""FirstRunnerBall""),originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=LEFTWING,weight=1.000000)),passOrKickTarget=1.000000,stayBehindSelectedPlayer=False,targettedAction=PASS,isFlipped=False),(name=""ScrumHalf"",priority=110.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY_PTB_RECEIVER,numPlayers=1)),actionList=(PASS,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""FlyHalf"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-3.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-5.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=-9.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=11.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=9.000000,g0=0.000000,g1=0.000000,pointType=0),(x=2.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHPASS,DECISION),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FLYHALF,weight=1.000000),(category=KICKING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""InsideCenter"",priority=101.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-10.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=17.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=PARTICIPATION,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=INSIDECENTER,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""OutsideCenter"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-22.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-28.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=18.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=0.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=OUTSIDECENTER,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""Fullback"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-27.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=20.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=FULLBACK,weight=1.000000),(category=DISTANCE,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=True,targettedAction=NOTHING,isFlipped=False),(name=""RightWing"",priority=109.000000,type=OPENSIDE_BIAS,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-50.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-50.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=30.000000,g0=0.000000,g1=0.000000,pointType=0),(x=1.000000,y=-20.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=3.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),playerMask=ALL,roles=((role=SETPLAY,numPlayers=1)),actionList=(NOTHING,CATCHKICK),syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000),(category=SPEED,weight=0.500000),(category=DISTANCE,weight=0.500000)),passOrKickTarget=2.000000,stayBehindSelectedPlayer=False,targettedAction=KICKPUNT,isFlipped=False),(name=""RightWingGetTheBall"",priority=100.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=),z=(points=),urgencyMode=MINMAX,minUrgency=0.000000,maxUrgency=0.000000,minUrgencyDistX=0.000000,maxUrgencyDistX=0.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=),height=(points=),playerMask=SETPLAYGTB,roles=((role=GETTHEBALL,numPlayers=1)),actionList=,syncPoints=,originTarget=SETPLAY_PTB_RECEIVER,groupStrategy=NOT_SET,setplayFitnessWeights=((category=RIGHTWING,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Behind"",priority=99.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.048742,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=7.761750,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=3)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""Forwards Inside"",priority=98.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=5.000000,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.000000,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.950000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=5.700135,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.740427,g0=0.000000,g1=0.000000,pointType=0))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=2)),actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=,passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False),(name=""ScrumHalfFollowBall"",priority=80.000000,type=STANDARD,movementType=NORMAL,mvParam1=0.000000,mvParam2=0.000000,x=(points=((x=0.000000,y=-0.061440,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.570143,g0=0.000000,g1=0.000000,pointType=0))),urgencyMode=MINMAX,minUrgency=0.200000,maxUrgency=0.700000,minUrgencyDistX=5.000000,maxUrgencyDistX=2.000000,breakTackleBoost=1.000000,positioning=SPOKE,width=(points=((x=0.000000,y=1.889857,g0=0.000000,g1=0.000000,pointType=0))),height=(points=((x=0.000000,y=1.775630,g0=0.000000,g1=0.000000,pointType=0))),playerMask=P9,roles=,actionList=,syncPoints=,originTarget=TRACKBALL,groupStrategy=NOT_SET,setplayFitnessWeights=((category=SCRUMHALF,weight=1.000000)),passOrKickTarget=0.000000,stayBehindSelectedPlayer=False,targettedAction=NOTHING,isFlipped=False))","((name=""Breakdown Support"",priority=50.000000,spacing=3.000000,minSpacing=1.000000,urgencyMode=MINMAX,minUrgency=0.900000,maxUrgency=0.900000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=0.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-9.204600,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.401200,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=),z=(points=)),(x=(points=((x=0.000000,y=8.504250,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=2.301150,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=FORWARDS,roles=((role=FORMATION,numPlayers=0)),originTarget=TRACKBALL,groupStrategy=NOT_SET,playerSortStrategy=RANDOM),(name=""Backs:Normal"",priority=2.000000,spacing=5.000000,minSpacing=3.000000,urgencyMode=PARTICIPATION,minUrgency=0.200000,maxUrgency=0.950000,minUrgencyDistX=27.000000,maxUrgencyDistX=5.000000,type=BIASMINSPACING,mvParam1=5.000000,mvParam2=0.000000,breakTackleBoost=1.000000,points=((x=(points=((x=0.000000,y=-46.256969,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.400000,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=-4.685995,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.970184,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=4.630318,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=3.894752,g0=0.000000,g1=0.000000,pointType=0)))),(x=(points=((x=0.000000,y=46.223099,g0=0.000000,g1=0.000000,pointType=0))),z=(points=((x=0.000000,y=14.407200,g0=0.000000,g1=0.000000,pointType=0))))),playerMask=BACKSTHENFORWARDS,roles=((role=FORMATION,numPlayers=7)),originTarget=INHERIT,groupStrategy=NOT_SET,playerSortStrategy=RANDOM))",""

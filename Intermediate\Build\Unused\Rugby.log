﻿  *** Found installed NintendoSDK version 19.3.5.  That version is greater than the maximum suggested version of 16.2.6 and is unverified. ***
  Building RugbyEditor and ShaderCompileWorker...
  Using Visual Studio 2019 14.29.30159 toolchain (C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133) and Windows 10.0.22000.0 SDK (C:\Program Files (x86)\Windows Kits\10).
  [Upgrade]
  [Upgrade] Using backward-compatible build settings. The latest version of UE4 sets the following values by default, which may require code changes:
  [Upgrade]     bLegacyPublicIncludePaths = false                 => Omits subfolders from public include paths to reduce compiler command line length. (Previously: true).
  [Upgrade]     ShadowVariableWarningLevel = WarningLevel.Error   => Treats shadowed variable warnings as errors. (Previously: WarningLevel.Warning).
  [Upgrade]     PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs   => Set in build.cs files to enables IWYU-style PCH model. See https://docs.unrealengine.com/en-US/Programming/BuildTools/UnrealBuildTool/IWYU/index.html. (Previously: PCHUsageMode.UseSharedPCHs).
  [Upgrade] Suppress this message by setting 'DefaultBuildSettings = BuildSettingsVersion.V2;' in RugbyEditor.Target.cs, and explicitly overriding settings that differ from the new defaults.
  [Upgrade]
  Building 6 actions with 24 processes...
    [1/6] Module.Rugby.11_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [2/6] Module.Rugby.33_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
    [3/6] Module.Rugby.13_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [4/6] UE4Editor-Rugby.lib
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.exp
    [5/6] UE4Editor-Rugby.dll
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.exp
    [6/6] RugbyEditor.target
  Total time in Parallel executor: 29.02 seconds
  Total execution time: 30.39 seconds

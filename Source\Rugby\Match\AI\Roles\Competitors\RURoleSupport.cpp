/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/RURoleSupport.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameSettings.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

MABRUNTIMETYPE_IMP1( RURoleSupport, RURoleSupportBase )

RURoleSupport::RURoleSupport( SIFGameWorld* game )
: RURoleSupportBase( game )
, force_waypoint_change_timer()
{
}

void RURoleSupport::Enter( ARugbyCharacter* player )
{
	RURoleSupportBase::Enter( player );
//RUPORT
//	player->GetAnimation()->SetIdleEnabled( true );
//	player->GetAnimation()->SetSupportEnabled( true );
	m_lock_manager.UFLock( UF_SETAIMOTIONLIMITS );

	// Initialise initial position
	//old_position = GetSupportPosition();
	force_waypoint_change_timer.Reset( m_pGame->GetSimTime(), 0.3f, true );
}

void RURoleSupport::UpdateLogic( const MabTimeStep& game_time_step )
{
	SIFGameWorld* game = m_pPlayer->GetGameWorld();
	MABASSERT( game != NULL );

	RUPlayerAttributes *plr_attribs = m_pPlayer->GetAttributes();
	RUPlayerMovement *plr_movement = m_pPlayer->GetMovement();


	RUTeam* team = plr_attribs->GetTeam();
	MABASSERT( team != NULL );

	// only alter speeds of supporters if they are not running some aggregate which is doing
	// its own way-pointing.
	if ( !m_lock_manager.UFIsLocked( UF_SETWAYPOINT ) )
	{
		// Alter speed and acceleration characteristics of supporters
		// Must be done after base update because RURole updates this info also

		const float DIFFICULTY_MULTIPLIER_START = 0.96f;
		const float DIFFICULTY_MULTIPLIER_INC_PER_LEVEL   = 0.04f;
		float difficulty_multiplier = 1.0f;

		if ( team->GetNumHumanPlayers() == 0 )
			difficulty_multiplier = DIFFICULTY_MULTIPLIER_START + game->GetGameSettings().difficulty * DIFFICULTY_MULTIPLIER_INC_PER_LEVEL;

		// TODO : Need some difficulty based support for support players
		//plr_movement->ResetMaxSpeedToBase( 1.10f * difficulty_multiplier ); /* 10% faster for supporters so they can catch up */
		//plr_movement->ResetMaxAccelerationToBase( 1.10f * difficulty_multiplier ); /* 10% better acceleration for supporters so they can catch up */
	}

	if (!m_lock_manager.UFIsLocked(UF_SETAGGRESSION))
	{
		wwNETWORK_TRACE_JG("AccumulateAggression {%4d} %s", __LINE__, __FILE__);
		m_pPlayer->GetAttributes()->AccumulateAggression(0.4f);
	}

	UpdateSupportFacingAngle();

	bool should_force_change = force_waypoint_change_timer.GetNumTimerEventsRaised() > 0;
	if ( should_force_change )
	{
		force_waypoint_change_timer.Reset(game->GetSimTime(), 0.1f + game->GetRNG()->RAND_RANGED_CALL(float, 0.2f));
	}

	bool should_change = should_force_change || plr_movement->ShouldChangeWaypoint();

	if ( m_pPlayer->GetHumanPlayer() != NULL )
	{

		// Update speed and get out of here
		plr_movement->SetTargetSpeed( plr_movement->GetIdealSpeed( AS_SPRINT ) );

	}
	else if ( !m_lock_manager.UFIsLocked( UF_SETWAYPOINT ) && should_change )
	{

		m_pPlayer->GetMovement()->SetFaceMotionSpeed( plr_movement->GetIdealSpeed( AS_RUN ) );

		float target_speed = 0.0f;
		FVector position = CalculateNextSupportWaypoint( target_speed );
		//MABLOGMSG(LOGCHANNEL_ALWAYS, LOGTYPE_ALWAYS, "%08x-%d: %f %f", player->GetGame(), player->GetAttributes()->GetIndex(), position.x, position.z);

		plr_movement->SetTargetSpeed( target_speed );
		wwNETWORK_TRACE_JG("SetTargetPosition {%4d} %s", __LINE__, __FILE__);
		plr_movement->SetTargetPosition( position );

		// Set the waypointing flags based on how close we are z wise to the
		// If we are behind the ball some way from our z then run at full tilt
	}

	if ( !m_lock_manager.UFIsLocked( UF_SETLOOK ) )
	{
		m_pPlayer->GetLookAt()->LookAtBallHolder();
	}

	RURoleSupportBase::UpdateLogic( game_time_step );
}

FVector RURoleSupport::GetIdealWaypoint()
{
	float temp_targ_speed = 0.0f;
	return CalculateNextSupportWaypoint( temp_targ_speed );
}

void RURoleSupport::Exit(bool forced)
{
	RURoleSupportBase::Exit(forced);
}

int RURoleSupport::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	SIFGameWorld *game = player->GetGameWorld();
	MABASSERT(game!=NULL);

	RUGameState* gamestate = game->GetGameState();

	// If nobody has the ball, support is useless
	if ( gamestate->GetBallHolder() == NULL && gamestate->GetLastBallHolder() == NULL )
		return 0;

	// if I have the ball, I can't be support
	if ( gamestate->GetBallHolder() == player )
		return 0;

	// Calculates a value between 0 -> 80 based on distance to ball
	float distance = game->GetSpatialHelper()->GetPlayerToBallDistance( player );
	MabMath::ClampUpper( distance, 50.0f );

	// Basic proximity fitness based on distance
	int proximityFitness = 50 - (int) distance;

	// Use enhanced position-based fitness if area has specific player mask
	if (area != nullptr)
	{
		ERugbyFormationPlayerMask playerMask = area->get_player_mask();
		return SSEVDSFormationManager::GetPositionBasedFitness(player, playerMask, proximityFitness);
	}

	// Fallback to proximity-based fitness
	return proximityFitness;
}

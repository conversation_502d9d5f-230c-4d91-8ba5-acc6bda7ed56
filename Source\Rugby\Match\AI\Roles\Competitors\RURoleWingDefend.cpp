/*-------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/RURoleWingDefend.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSSpatialHelper.h"
#include "Utility/RURandomNumberGenerator.h"

#include "Engine/Public/DrawDebugHelpers.h"

//#rc3_legacy_include #include "SIFDebug.h"

// Wait a second before charging in
#define MIN_WAIT_TIME	0.8f
#define MAX_WAIT_TIME	1.8f

/// --------------------------------------------------------------------------
/// Left/Right variations
/// --------------------------------------------------------------------------

MABRUNTIMETYPE_IMP1( RURoleLeftWingDefend, RURoleWingDefend )
MABRUNTIMETYPE_IMP1( RURoleRightWingDefend, RURoleWingDefend )

RURoleLeftWingDefend::RURoleLeftWingDefend( SIFGameWorld* game ) : RURoleWingDefend(game)
{
}

RURoleRightWingDefend::RURoleRightWingDefend( SIFGameWorld* game ) : RURoleWingDefend(game)
{
}

/// --------------------------------------------------------------------------
/// Base wing defend
/// --------------------------------------------------------------------------

MABRUNTIMETYPE_IMP1( RURoleWingDefend, RURoleBaseDefend )

RURoleWingDefend::RURoleWingDefend(SIFGameWorld* game)
: RURoleBaseDefend(game)
, old_position(0.0f, 0.0f, 0.0f)
, wait_time(0.0f)
, ai_think_timer(0.0f)
, random_num_offside_tackle_probability(0.0f)
{

}

RURoleWingDefend::~RURoleWingDefend()
{
}

void RURoleWingDefend::Enter( ARugbyCharacter* player )
{
	RURoleBaseDefend::Enter( player );
}

void RURoleWingDefend::UpdateLogic(  const MabTimeStep& game_time_step  )
{
	// update ai think speed and any random numbers they have
	const float AI_THINK_SPEED = 0.3f;	// rate at which random numbers are updated to make new decisions
	ai_think_timer -= game_time_step.delta_time.ToSeconds();
	if (ai_think_timer < 0.0f)
	{
		ai_think_timer = AI_THINK_SPEED;
		// update ai random numbers
		random_num_offside_tackle_probability = m_pGame->GetRNG()->RAND_CALL(float);
	}

	// Can we tackle?
	RUTackleHelper* tackle_helper = m_pGame->GetTackleHelper();
	if ( tackle_helper->ShouldDoAnAITackle( m_pPlayer, random_num_offside_tackle_probability ) && tackle_helper->IsTackleCandidate( m_pPlayer ) ) {
		StartActionTackle( false, SSS_UNKNOWN );
	}

#ifdef BUILD_DEBUG
	DrawDefendArea();
#endif

	wwNETWORK_TRACE_JG("SetParticipationLevel {%4d} %s", __LINE__, __FILE__);
	m_pPlayer->GetState()->SetParticipationLevel( PL_INTERESTED );

	// Update common stuff
	if ( !m_lock_manager.UFIsLocked( UF_SETLOOK ) )
	{
		m_pPlayer->GetLookAt()->LookAtBallHolder();
	}

	RURoleBaseDefend::UpdateLogic( game_time_step );
}

void RURoleWingDefend::Exit(bool forced)
{
	RURoleBaseDefend::Exit(forced);
}

int RURoleWingDefend::GetPossibleDefendOptions()
{
	return RU_RD_ALL;
}

#ifdef BUILD_DEBUG
void RURoleWingDefend::DrawDefendArea()
{

	//Debug drawing
	FVector slot_center, slot_size;
	if (player &&
		player->GetAttributes() &&
		player->GetAttributes()->GetTeam())
	{
		if (SSEVDSFormationManager * form_manager = player->GetAttributes()->GetTeam()->GetFormationManager())
		{
			if (SSRoleArea * targetArea = const_cast<SSRoleArea*>(form_manager->GetPlayerArea(player)))
			{
				FVector originPos = form_manager->GetOrigin();
				FVector targetPos = targetArea->GetZonePosition(player->GetAttributes()->GetTeam()->GetRight());
				slot_center = targetPos;
				slot_size = FVector(targetArea->get_zone().width.get_graph_value(0), 0.3f, targetArea->get_zone().height.get_graph_value(0));
				FVector movementTargetPos = player->GetMovement()->GetTargetPosition();
				FVector playerPos = player->GetMovement()->GetCurrentPosition();


				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(slot_center, converted_centre);
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(slot_size, converted_slot_size);
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(originPos, origin_converted);
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(playerPos, playerPos_converted);
				MAB_VEC_TO_UNREAL_VEC_FOR_TRANSLATION(movementTargetPos, movementTargetPos_converted);

				/* var list
				const UWorld* InWorld,
				FVector const& Center,
				FVector const& Extent,
				const FQuat& Rotation,
				FColor const& Color,
				bool bPersistentLines = false,
				float LifeTime=-1.f,
				uint8 DepthPriority = 0,
				float Thickness = 0.f);
				*/
				//Target zone
				DrawDebugBox(
					player->GetWorld(),
					converted_centre,
					converted_slot_size,
					FQuat::Identity,
					FColor(255, 0, 0),
					false,
					-1,
					10,
					20
				);

				//Origin
				DrawDebugBox(
					player->GetWorld(),
					origin_converted,
					converted_slot_size,
					FQuat::Identity,
					FColor(0, 0, 255),
					false,
					-1,
					10,
					20
				);

				//Line to movement target pos
				DrawDebugLine(
					player->GetWorld(),
					playerPos_converted,
					movementTargetPos_converted,
					FColor(1, 1, 0),
					false,
					-1,
					10,
					20
				);



			}
		}
	}
	//MabDebugDraw *ddraw = getfield()->GetDebugDraw();

	
	//ddraw->SetBox( (long)this, slot_center, slot_size, MabColour::Gray( 1.0f, 0.3f ) );
}
#endif

int RURoleWingDefend::GetFitness(const ARugbyCharacter* player, const SSRoleArea*, const int wing_side)
{
	SIFGameWorld* game = player->GetGameWorld();
	PLAYER_POSITION position = player->GetAttributes()->GetPlayerPosition();

	// Prioritize actual wing players based on their position numbers
	// P2 (Right Wing) should get right wing roles, P5 (Left Wing) should get left wing roles
	if (wing_side == WS_RIGHT && (position & PP_RIGHTWING))
	{
		return 100; // High priority for correct wing player
	}
	else if (wing_side == WS_LEFT && (position & PP_LEFTWING))
	{
		return 100; // High priority for correct wing player
	}

	// Fallback to proximity-based selection for other players
	FieldExtents field_extents = game->GetSpatialHelper()->GetFieldExtentsExcludingInGoals();
	float play_dir = -(float)player->GetAttributes()->GetPlayDirection();
	float sideline_x = field_extents.x * 0.5f * wing_side * play_dir;
	float dist_to_sideline = MabMath::Fabs( sideline_x - player->GetMovement()->GetCurrentPosition().x);

	int priority = (int) ((70.0f  - dist_to_sideline) * 0.5f);
	MabMath::ClampLower( priority, 0 );

	// Give general wing players a moderate boost
	if ( position & PP_WING )
	{
		priority += 15;
	}

	// priority must be 1 or more
	return priority + 1;
}

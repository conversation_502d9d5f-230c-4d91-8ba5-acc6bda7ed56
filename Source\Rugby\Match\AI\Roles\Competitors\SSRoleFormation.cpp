/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/AI/Roles/Competitors/SSRoleFormation.h"

#include "Mab/AdvMath/MabAdvMath.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerLookAt.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/RUPlayerState.h"
#include "Character/RugbyPlayerController.h"
#include "Match/Cutscenes/SSCutSceneManager.h"
#include "Match/RugbyUnion/RUDBTeam.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUMotionSource.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSMath.h"
#include "Match/SSSpatialHelper.h"

MABRUNTIMETYPE_IMP1( SSRoleFormation, SSRole );


///-------------------------------------------------------------------------
/// Constructor
///-------------------------------------------------------------------------

SSRoleFormation::SSRoleFormation( SIFGameWorld* game ) : SSRole(game)
{
}

///-------------------------------------------------------------------------
/// Called when role starts.
///-------------------------------------------------------------------------

class SSFormationMovementSource : public RUMotionSource
{
public:
	SSFormationMovementSource( ARugbyCharacter* player, SSEVDSFormationManager* formation_manager ) : player( player ), formation_manager( formation_manager )
	{
		local_time = 0.0f;
	}

	virtual bool GetFrame( MotionFrame& frame ) const
	{
		frame.state.current_position = new_pos;
		frame.time = local_time;

		return true;
	}

	virtual void Update( float delta_time )
	{
		//const static float CONVERGENCE_TIME = 1.0f;
		const static float MIN_URGENCY_TIME = 0.75f;
		const static float MAX_URGENCY_TIME = 2.0f;
		const static float CONVERGENCE_AMOUNT = 0.95f; // Don't set to 1 as this won't really work

		FVector curr_pos = player->GetMovement()->GetCurrentPosition();
		float urgency;
		FVector targ_pos;
		ACTOR_SPEED max_actor_speed;

		wwNETWORK_TRACE_JG("SSFormationMovementSource ID: %u, Delta: %f, Position X: %f, Position Y: %f, Position Z: %f", player->GetAttributes()->GetDbId(), delta_time, curr_pos.X, curr_pos.Y, curr_pos.Z);

		formation_manager->GetStandardFormationMovement( player, targ_pos, urgency, max_actor_speed );

		wwNETWORK_TRACE_JG("SSFormationMovementSource targ_pos X: %f, targ_pos Y: %f, targ_pos Z: %f, urgency: %f, max_actor_speed: %d",	targ_pos.X, targ_pos.Y, targ_pos.Z, urgency, max_actor_speed);

		MabMath::Clamp( urgency, 0.0f, 1.0f );
		float convergence_time = MabMath::Lerp( MIN_URGENCY_TIME, MAX_URGENCY_TIME, urgency );

		new_pos = MabMath::Lerp( targ_pos, curr_pos, MabMath::Pow( 1.0f - CONVERGENCE_AMOUNT, delta_time / convergence_time ) );

		FVector delta_pos = new_pos - curr_pos;
		FVector new_vel = delta_pos / delta_time;

		new_vel = new_vel.Unit() * MabMath::Min( new_vel.Magnitude(), player->GetMovement()->GetCurrentMaxPlaySpeed() );
		//float accel = player->GetMovement()->GetMaxAcceleration( player->GetMovement()->GetCurrentVelocity());
		//FVector dv = player->GetMovement()->GetCurrentVelocity() - new_vel;
		new_pos = curr_pos + new_vel * delta_time;

		wwNETWORK_TRACE_JG_DISABLED("SSFormationMovementSource new_vel X: %f, new_vel Y: %f, new_vel Z: %f", new_vel.X, new_vel.Y, new_vel.Z);

		wwNETWORK_TRACE_JG_DISABLED("SSFormationMovementSource new_pos X: %f, new_pos Y: %f, new_pos Z: %f",	new_pos.X, new_pos.Y, new_pos.Z);

		local_time += delta_time;
	}

	/// Query interface for the type of motion information that this motion source provides
	virtual bool ProvidesPositions() const { return true; }
	virtual bool ProvidesFacing() const { return false; }

private:
	ARugbyCharacter* player;
	SSEVDSFormationManager* formation_manager;
	FVector new_pos;
	float local_time;
};
void SSRoleFormation::Enter( ARugbyCharacter* player )
{
	SSRole::Enter( player );
	// GGs JZ
	if (m_pGame->GetGameState()->GetPhase() == RUGamePhase::PLAY_THE_BALL)
	{
		m_lock_manager.HFUnlock(HF_CHANGE_PLAYER);
		m_lock_manager.UFUnlock(UF_SETWAYPOINT);
		m_pActionManager->EnableAllActions(false);
	}
	else
	{
		player->GetActionManager()->EnableAllActions( true );
	}
}

///-------------------------------------------------------------------------
/// Called when role exits.
///-------------------------------------------------------------------------

void SSRoleFormation::Exit(bool forced)
{
	// GGs JZ not sure about this
	m_bWarped = false;
	m_lock_manager.HFClearLocks();
	m_pActionManager->EnableAllActions(true);
	SSRole::Exit(forced);
}

///-------------------------------------------------------------------------
/// UpdateLogic
///-------------------------------------------------------------------------

void SSRoleFormation::UpdateLogic( const MabTimeStep& game_time_step )
{
	MABASSERT( m_pPlayer != NULL );

	SSEVDSFormationManager *formation_manager = m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager();

	SSRole::UpdateLogic( game_time_step );

	RUGamePhase phase = m_pGame->GetGameState()->GetPhase();
	bool force_positioning = (phase == RUGamePhase::PLAY_THE_BALL || phase==RUGamePhase::KICK_OFF || phase==RUGamePhase::CONVERSION || phase==RUGamePhase::DROPOUT || phase==RUGamePhase::LINEOUT || phase==RUGamePhase::PENALTY_SHOOT_FOR_GOAL || phase==RUGamePhase::PENALTY_KICK_FOR_TOUCH/* || phase==RUGamePhase::DECISION_PENALTY*/);
	bool assign_position = (force_positioning || m_pPlayer->GetHumanPlayer()==NULL);
	bool should_change_wp = false;
	/// Retain our slot in the formation manager
	formation_manager->UpdatePlayerSlot( m_pPlayer );
	if (m_pGame->GetGameState()->GetPhase() == RUGamePhase::PLAY_THE_BALL)
	{
		if (!m_bWarped && m_pGame->GetGameState()->IsZeroTackle())
		{	
			if (currentWaitDuration <= waitBeforeWarpingDuration)
			{
				currentWaitDuration += game_time_step.delta_time;
				return;
			}
			else
			{
				WarpToWaypoint();
				m_bWarped = true;
			}
		}
		should_change_wp = true;
	}
	else
	{
		should_change_wp = m_pPlayer->GetMovement()->ShouldChangeWaypoint();
	}


	wwNETWORK_TRACE_JG("SSRoleFormation Update Waypointing ID: %u, lm:%d, scw:%d, ap:%d", m_pPlayer->GetAttributes()->GetDbId(), m_lock_manager.UFIsLocked(UF_SETWAYPOINT), should_change_wp, assign_position);
	/// Update waypointing
	if (!m_lock_manager.UFIsLocked(UF_SETWAYPOINT) && should_change_wp && assign_position)
	{
		/// We should assign a target if we're in one of the set phases, are AI and
		/// it's time to change waypoint
		wwNETWORK_TRACE_JG("SSRoleFormation UpdateLogic DoStandardFormationMovement", m_pPlayer->GetAttributes()->GetDbId());
		formation_manager->DoStandardFormationMovement(m_pPlayer );
	}

	// HACK:- Make players face motion during kick off phase (cutscene).
	// ( Will probably have to make a new role - eventually ).

	if (!m_lock_manager.UFIsLocked(UF_SETFACING) )
	{
		DoFacing();
	}

	/// Update look behaviour
	if ( !m_lock_manager.UFIsLocked( UF_SETLOOK ) )
	{
		RUPlayerLookAt* look_at = m_pPlayer->GetLookAt();
		look_at->LookAtBallHolder();
	}

	wwNETWORK_TRACE_JG_DISABLED("SSRoleFormation Update Aggression ID: %u, lm:%d", m_pPlayer->GetAttributes()->GetDbId(), m_lock_manager.UFIsLocked(UF_SETAGGRESSION));
	/// Update Aggression
	if ( !m_lock_manager.UFIsLocked( UF_SETAGGRESSION ) )
	{
		wwNETWORK_TRACE_JG_DISABLED("SSRoleFormation UpdateLogic AccumulateAggression", m_pPlayer->GetAttributes()->GetDbId());
		bool is_attacking = m_pGame->GetGameState()->GetAttackingTeam() == m_pPlayer->GetAttributes()->GetTeam();
		m_pPlayer->GetAttributes()->AccumulateAggression( is_attacking ? 0.4f : 0.5f );
	}
}

///-------------------------------------------------------------------------
/// UpdateCutScene
///-------------------------------------------------------------------------

void SSRoleFormation::UpdateCutScene( const MabTimeStep& game_time_step )
{
	wwNETWORK_TRACE_JG_DISABLED("SSRoleFormation UpdateCutScene ID: %u", m_pPlayer->GetAttributes()->GetDbId());
	UpdateLogic(game_time_step);

	SSCutSceneManager *cutscene_manager = m_pGame->GetCutSceneManager();
	cutscene_manager->PostProcessNonActorMovement(m_pPlayer);
}

///-------------------------------------------------------------------------
///
///-------------------------------------------------------------------------

int SSRoleFormation::GetFitness(const ARugbyCharacter* player, const SSRoleArea* area)
{
	// Calculate proximity-based fitness first
	constexpr const float DIVISOR = 3.0f;
	int proximityFitness;

	if ( area->is_line() )
	{
		const FVector lineStart = area->GetPathPosition( 0.0f, true );
		const FVector lineEnd = area->GetPathPosition( 1.0f, true );
		float t;
		float dist_to_seg = MabMath::Sqrt( MabAdvMath::SquaredDistanceFromPointToSegment( player->GetMovement()->GetCurrentPosition(), lineStart, lineEnd - lineStart, t ) );
		dist_to_seg /= DIVISOR;

		proximityFitness = 100 - (int) dist_to_seg;
	} else {

		FVector zone_origin = area->GetZonePosition( -float(player->GetAttributes()->GetTeam()->GetPlayDirection()) );
		float dist_to_origin = SSMath::GetXZPointToPointDistance( zone_origin, player->GetMovement()->GetCurrentPosition() );
		dist_to_origin /= DIVISOR;
		proximityFitness = 100 - (int) dist_to_origin;
	}

	// Use enhanced position-based fitness that combines position priority with proximity
	ERugbyFormationPlayerMask playerMask = area->get_player_mask();
	return SSEVDSFormationManager::GetPositionBasedFitness(player, playerMask, proximityFitness);
}

void SSRoleFormation::WarpToWaypoint()
{
	RUGamePhase phase = m_pGame->GetGameState()->GetPhase();
	wwNETWORK_TRACE_JG_DISABLED("SSRoleFormation Warp to Waypoint ID: %u", m_pPlayer->GetAttributes()->GetDbId());
	SSEVDSFormationManager* formation_manager = m_pPlayer->GetAttributes()->GetTeam()->GetFormationManager();

	if (phase == RUGamePhase::PLAY_THE_BALL)
	{
		RUPlayerAttributes* attribs = m_pPlayer->GetAttributes();
		SSRoleArea* area = formation_manager->GetPlayerArea(m_pPlayer);
		FVector position = m_pPlayer->GetMovement()->GetCurrentPosition();
		/// Use the current formation area (zone) to set the player's position
		int slot_idx = area->UpdatePlayerSlot(m_pPlayer);
		float play_dir = -(float)attribs->GetPlayDirection();

		position = area->GetZonePosition(play_dir, 0);

		float dx, dz;
		area->GetPlayerZonePosition(m_pPlayer, slot_idx, dx, dz, 0);
		position.x += dx;
		position.z += dz;

		m_pPlayer->GetMovement()->SetTargetPosition(position);
	}
	else
	{
		formation_manager->DoStandardFormationMovement( m_pPlayer );
	}

	DoFacing();

	SSRole::WarpToWaypoint();
}

void SSRoleFormation::DoFacing()
{
	wwNETWORK_TRACE_JG_DISABLED("SSRoleFormation DoFacing ID: %u", m_pPlayer->GetAttributes()->GetDbId());
	/// Kick off facing
	RUGamePhase phase = m_pGame->GetGameState()->GetPhase();
	if(phase==RUGamePhase::KICK_OFF)
	{
		m_pMovement->SetFacingFlags( AFFLAG_FACEPLAYDIR );
		m_pMovement->SetFaceMotionSpeed( m_pPlayer->GetMovement()->GetIdealSpeed( AS_JOG ) );

		/// Non standard play facing
	} else if ( !m_pGame->GetGameState()->IsGameInStandardPlay() ) {
		m_pMovement->SetFacingFlags( AFFLAG_FACEBALL_PARTIAL_INTEREST );
	} else { // Default facing behaviour

		/// If the ball holder is moving away from us then face him otherwise
		/// Try to face forward
		ARugbyCharacter* ball_holder = m_pGame->GetGameState()->GetBallHolder();
		if ( !ball_holder && !m_pGame->GetStrategyHelper()->IsBallReallyFree() )
			ball_holder = m_pGame->GetGameState()->GetLastBallHolder();

		bool face_bh = false;
		if ( ball_holder )
		{
			RUPlayerMovement* pBallHolderMovement = ball_holder->GetMovement();
			float bh_closure_on_us = MabAdvMath::GetClosureRate(pBallHolderMovement->GetCurrentPosition(), pBallHolderMovement->GetCurrentVelocity(), m_pPlayer->GetMovement()->GetCurrentPosition(), FVector::ZeroVector );
			face_bh = bh_closure_on_us < 0.0f;
		}

		PARTICIPATION_LEVEL pl = m_pPlayer->GetState()->GetParticipationLevel();
		face_bh = face_bh || pl == PL_OBSERVING || pl == PL_INTERESTED;

		if ( face_bh )
			m_pMovement->SetFacingFlags( AFFLAG_FACEBALL );
		else
			// Bias in towards the ball
			m_pMovement->SetFacingFlags( AFFLAG_FACEOPPGOALLINE_SMART );

		m_pMovement->SetFaceMotionSpeed( m_pMovement->GetIdealSpeed( AS_FASTRUN ) );
	}
}

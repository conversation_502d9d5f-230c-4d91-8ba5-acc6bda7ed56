/*--------------------------------------------------------------
|        Copyright (C) 1997-2011 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/TutorialMode/Tutorials/RUTutorialAdvLooseBall.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/SIFGameWorld.h"

#include "Character/RugbyCharacter.h"

namespace RUTUTORIALADVLOOSEBALL
{
	// Medal times
	static const float GOLD_TIME = 7.0f;
	static const float SILVER_TIME = 12.0f;
	static const float BRONZE_TIME = 300.0f;

	static const char* TUT_ID = "ID_TUTORIAL_LOOSE_ADVANCED";
	static const float HELD_TOO_LONG_TIME = 1.0f;
}

RUTutorialAdvLooseBall::RUTutorialAdvLooseBall(SIFGameWorld* game)
: RUTutorial(game, RUTutorialType::LOOSE_ADVANCED, RUTutorialGroup::LESSON3, "ruged/tutorials/adv_loose_ball.json"),
  opp_player( NULL ),
  last_group( GTB_ACTION::INVALID ),
  ball_held_timer( 0.0f ),
  force_pickup( true )
{
}

RUTutorialAdvLooseBall::~RUTutorialAdvLooseBall()
{

}

void RUTutorialAdvLooseBall::Update(float delta_time)
{
	if ( game->GetGameState()->GetBallHolder() == m_player && !GetSuccessState() )
	{
		ball_held_timer += delta_time;
		if ( ball_held_timer > RUTUTORIALADVLOOSEBALL::HELD_TOO_LONG_TIME && !GetFailureState())
		{
			mFailString = "[ID_TUTORIAL_LOOSE_ADVANCED_FAIL_01]";
			SetFailureState( true );
		}
	}

	MABASSERT(m_player);
	if ( !m_player->GetMovement()->HasReachedWaypoint() )
	{
		if (m_player->GetActionManager()->UFIsLocked( UF_DOMOTION ) && !mMedalTimer.GetEnabled() )
		{
			m_player->GetActionManager()->UFUnlock( UF_DOMOTION );

			SSEVDSFormationManager *t1_formation_manager = game->GetTeam(0)->GetFormationManager();
			t1_formation_manager->ForceFormation("Tutorial",1);

			StartMedalTimer();
			game->GetBall()->PropelBall(PI, 0.000001f, true);
		}
	}

	// Force pickup. Only force once or GTB goes crazy.
	if (m_player->GetRole() && m_player->GetRole()->RTTGetType() == RURoleGetTheBall::RTTGetStaticType() && force_pickup )
	{
		RURoleGetTheBall* gtb_role = m_player->GetRole< RURoleGetTheBall >();
		gtb_role->ForceAction( GTB_ACTION::PICKUP );
		force_pickup = false;
	}
}

bool RUTutorialAdvLooseBall::HasSucceeded()
{
	return GetSuccessState();
}

bool RUTutorialAdvLooseBall::HasFailed()
{
	return GetFailureState();
}

void RUTutorialAdvLooseBall::Start()
{
	game->GetRules()->EnableTriggers( RURT_TRYSUCCESS );
	game->GetRules()->EnableConsequences( true );

	game->GetTutorialManager()->DisplayObjective( RUTUTORIALADVLOOSEBALL::TUT_ID );

	game->GetGameState()->SetBallHolder( NULL );
	game->GetBall()->SetRotation( MabQuaternion::IDENTITY );
	game->GetBall()->SetPositionAbsolute( FVector(15.0f,0.05f, 36.0f) );

	SSEVDSFormationManager *t1_formation_manager = game->GetTeam(0)->GetFormationManager();
	t1_formation_manager->ForceClearFormation();

	// WJS RLC Is this meant to set class player??
	m_player = GetTutorialPlayer()->GetRugbyCharacter();
	MABASSERT(m_player);
	RUActionManager* action_manager = m_player->GetActionManager();
	MABASSERT( action_manager );

	if ( !action_manager->UFIsLocked( UF_DOMOTION ) )
		action_manager->UFLock( UF_DOMOTION );

	game->GetStrategyHelper()->OnTutorialSetBallLoose(m_player);

	ball_held_timer = 0.0f;

	force_pickup = true;
	last_group = GTB_ACTION::INVALID;

	SetSuccessState( false, true );
	SetFailureState( false, true );
}

void RUTutorialAdvLooseBall::Initialise()
{
	RUGameEvents* game_events = game->GetEvents();
	game_events->ball_dead_detected.Add( this, &RUTutorialAdvLooseBall::BallOut );
	game_events->ball_out_detected.Add( this, &RUTutorialAdvLooseBall::BallOut );
	game_events->try_result.Add( this, &RUTutorialAdvLooseBall::TryScored );

	game->GetTutorialManager()->SetStage( RUStageNum::ONE, "[ID_TUTORIAL_LOOSE_ADVANCED_STAGEONE]", "1", ERugbyGameAction::ANALOG_L );
	game->GetTutorialManager()->SetStage( RUStageNum::TWO, "[ID_TUTORIAL_LOOSE_ADVANCED_STAGETWO]", "1", ERugbyGameAction::LB_KICK );
	game->GetTutorialManager()->SetStage( RUStageNum::THREE, "[ID_TUTORIAL_LOOSE_ADVANCED_STAGETHREE]", "1", ERugbyGameAction::ANALOG_L );
	game->GetTutorialManager()->SetStage( RUStageNum::FOUR, "[ID_TUTORIAL_LOOSE_ADVANCED_STAGEFOUR]", "1", ERugbyGameAction::LB_DIVE );
}

void RUTutorialAdvLooseBall::Finish()
{
	if (m_player == nullptr)
	{
		return;
	}

	RUActionManager* action_manager = m_player->GetActionManager();
	MABASSERT( action_manager );
	if (m_player->GetActionManager())
	{
		if (action_manager->UFIsLocked(UF_DOMOTION))
		{
			action_manager->UFUnlock(UF_DOMOTION);
		}
	}
}

void RUTutorialAdvLooseBall::CleanUp()
{
#ifdef ENABLE_ANALYTICS
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkTutorialViewed(wwAnalyticsTutorialOption::KickLooseBall);
	}
#endif

	RUGameEvents* game_events = game->GetEvents();
	game_events->ball_dead_detected.Remove( this, &RUTutorialAdvLooseBall::BallOut );
	game_events->ball_out_detected.Remove( this, &RUTutorialAdvLooseBall::BallOut );
	game_events->try_result.Remove( this, &RUTutorialAdvLooseBall::TryScored );

	m_player = NULL;
}

void RUTutorialAdvLooseBall::CalculateMedal()
{
	float time = mMedalTimer.GetTimeSinceLastEvent();

	if ( time > RUTUTORIALADVLOOSEBALL::SILVER_TIME )
	{
		if ( mMedalAchievement < RUTutorialMedal::BRONZE )
		{
			game->GetTutorialManager()->DisplayAchievement( RUTutorialMedal::BRONZE );
			mMedalAchievement = RUTutorialMedal::BRONZE;
		}
		mSuccessString = "[ID_TUTORIAL_LOOSE_ADVANCED_SUCCESS_01]";
	}
	else if ( time < RUTUTORIALADVLOOSEBALL::SILVER_TIME && time > RUTUTORIALADVLOOSEBALL::GOLD_TIME )
	{
		if ( mMedalAchievement < RUTutorialMedal::SILVER )
		{
			game->GetTutorialManager()->DisplayAchievement( RUTutorialMedal::SILVER );
			mMedalAchievement = RUTutorialMedal::SILVER;
		}
		mSuccessString = "[ID_TUTORIAL_LOOSE_ADVANCED_SUCCESS_01]";
	}
	else if ( time < RUTUTORIALADVLOOSEBALL::GOLD_TIME )
	{
		if (mMedalAchievement < RUTutorialMedal::GOLD )
		{
			game->GetTutorialManager()->DisplayAchievement( RUTutorialMedal::GOLD );
			mMedalAchievement = RUTutorialMedal::GOLD;
		}
		mSuccessString = "[ID_TUTORIAL_LOOSE_ADVANCED_SUCCESS_02]";
	}
}

void RUTutorialAdvLooseBall::BallOut(ARugbyCharacter* /*ball_holder*/, const FVector& /*position*/, bool /*on_full*/ )
{
	if ( !GetSuccessState() )
	{
		mFailString = "[ID_TUTORIAL_WITHIN_FIELD_FAIL]";
		SetFailureState( true );
	}
	game->GetBall()->SetPositionAbsolute( FVector(15.0f,0.05f, 36.0f) );
}

void RUTutorialAdvLooseBall::TryScored( bool successful, bool /*penalty*/, ARugbyCharacter* /*scorer*/)
{
	if ( successful && !GetSuccessState() )
	{
		if ( !mDemoActive )
			CalculateMedal();

		SetSuccessState( true );
	}
	game->GetBall()->SetPositionAbsolute( FVector(15.0f,0.05f, 36.0f) );
}


bool RUTutorialAdvLooseBall::IsStageComplete( RUStageNum stageNum )
{
	static const float BALL_CLOSE_DISTANCE = 4.0f;

	if (stageNum == RUStageNum::ONE)
	{
		FVector cur_pos = m_player->GetMovement()->GetCurrentPosition();
		if (cur_pos.z > game->GetBall()->GetCurrentPosition().z - BALL_CLOSE_DISTANCE)
		{
			return true;
		}
	}
	else if (stageNum == RUStageNum::TWO)
	{
		if (m_player->GetActionManager()->IsActionRunning( ACTION_GETTHEBALL ) )
		{
			RUActionGetTheBall* gtb_action = m_player->GetActionManager()->GetAction< RUActionGetTheBall >();
			if ( last_group == GTB_ACTION::KICK && gtb_action->GetCollectType() != GTB_ACTION::KICK )
				return true;

			last_group = gtb_action->GetCollectType();
		}
	}
	else if (stageNum == RUStageNum::THREE)
	{
		FVector cur_pos = m_player->GetMovement()->GetCurrentPosition();
		// Have we moved past cones?
		FVector ball_pos = game->GetBall()->GetCurrentPosition();
		if (cur_pos.z > ball_pos.z - BALL_CLOSE_DISTANCE &&
				ball_pos.z > FIELD_LENGTH * 0.5f )
		{
			return true;
		}
	}
	else if (stageNum == RUStageNum::FOUR)
	{
		if( GetSuccessState() && !GetFailureState())
		{
			return true;
		}
	}

	return false;
}

bool RUTutorialAdvLooseBall::IsStageFailed( RUStageNum stage_num )
{
	switch ( stage_num )
	{
	case RUStageNum::ONE:
		{
			return false;
		}
		break;
	case RUStageNum::TWO:
		{
			//FVector cur_pos = player->GetMovement()->GetCurrentPosition();
			//// Have we moved past cones?
			//if (cur_pos.z < cone_list.back().position.z)
			//{
			//	return true;
			//}
			return false;
		}
		break;
	case RUStageNum::THREE:
		{
			if (m_player->GetMovement()->GetCurrentPosition().z < FIELD_LENGTH * 0.5f )
			{
				return false;
			}
		}
		break;
	case RUStageNum::FOUR:
		{

		}
		break;
	default: break;
	}

	return false;
}

float RUTutorialAdvLooseBall::GetGoldMedalTime()
{
	return RUTUTORIALADVLOOSEBALL::GOLD_TIME;
}

float RUTutorialAdvLooseBall::GetSilverMedalTime()
{
	return RUTUTORIALADVLOOSEBALL::SILVER_TIME;
}

float RUTutorialAdvLooseBall::GetBronzeMedalTime()
{
	return RUTUTORIALADVLOOSEBALL::BRONZE_TIME;
}

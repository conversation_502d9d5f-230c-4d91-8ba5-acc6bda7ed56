/*--------------------------------------------------------------
|        Copyright (C) 1997-2010 by Prodigy Design Ltd         |
|		              Sidhe Interactive (TM)		           |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/TutorialMode/Tutorials/RUTutorialKickingCancel.h"

#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Character/RugbyPlayerController.h"
#include "Match/RugbyUnion/RUGameEvents.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/Rules/RURuleTriggerEnum.h"
#include "Match/RugbyUnion/Rules/RURules.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/SIFGameWorld.h"
#include "Match/Components/SSHumanPlayer.h"

namespace RUTUTORIALKICKINGCANCEL
{
	// No local settings
}

RUTutorialKickingCancel::RUTutorialKickingCancel( SIFGameWorld* game )
: RUTutorial( game, RUTutorialType::KICKING_CANCEL, RUTutorialGroup::LESSON4, "ruged/tutorials/kick_cancel.json" ),
  kick_started( false ),
  ball_passed( false )
{

}

// Class deconstructor.
RUTutorialKickingCancel::~RUTutorialKickingCancel()
{

}

// Perform logic. Check state. Do anything appropriate.
void RUTutorialKickingCancel::Update( float delta_time )
{
	MABUNUSED(delta_time);

	if ( kick_started && m_player->GetActionManager()->UFIsLocked( UF_DOMOTION ) )
	{
		//m_player->GetActionManager()->HFUnlock( HF_KICK );

		SIFRugbyCharacterList opp_players = m_player->GetAttributes()->GetOppositionTeam()->GetPlayers();

		for ( SIFRugbyCharacterList::const_iterator iter = opp_players.begin(); iter != opp_players.end(); ++iter )
		{
			if ( (*iter)->GetActionManager()->UFIsLocked( UF_DOMOTION ) )
				(*iter)->GetActionManager()->UFUnlock( UF_DOMOTION );
		}

		m_player->GetActionManager()->UFUnlock( UF_DOMOTION );
	}
}

// Start the tutorial
void RUTutorialKickingCancel::Initialise()
{
		// turn off game rules (stop player reposition after out of bounds)
	game->GetRules()->EnableConsequences( false );
	game->GetRules()->EnableTriggers( RURT_NONE );

	// Hook up to events
	RUGameEvents* game_events = game->GetEvents();
	game_events->ball_dead_detected.Add( this, &RUTutorialKickingCancel::BallInTouch );
	game_events->ball_out_detected.Add( this, &RUTutorialKickingCancel::BallInTouch );
	game_events->ball_bounce.Add( this, &RUTutorialKickingCancel::BallBounce );
	game_events->pass.Add( this, &RUTutorialKickingCancel::OnPass );
	game_events->ball_holder_changed.Add( this, &RUTutorialKickingCancel::OnBallHolderChanged );
	game_events->tackle.Add( this, &RUTutorialKickingCancel::OnTackle );

	MabVector<ERugbyGameAction> actions;
	actions.reserve(2);
	actions.push_back(ERugbyGameAction::PASS_LEFT_SINGLE);
	actions.push_back(ERugbyGameAction::PASS_RIGHT_SINGLE);

	game->GetTutorialManager()->SetStage(RUStageNum::ONE, "[ID_TUTORIAL_KICKING_CANCEL_STAGEONE]", "1", ERugbyGameAction::KICK_PUNT);
	game->GetTutorialManager()->SetStage(RUStageNum::TWO, "[ID_TUTORIAL_KICKING_CANCEL_STAGETWO]", "1", actions);
}

// Start the tutorial
void RUTutorialKickingCancel::Start()
{
	MabString name(32, "ID_TUTORIAL_KICKING_CANCEL");
	game->GetTutorialManager()->DisplayObjective( name );

	// If no ball holder, give to the m_player
	if (!m_player || (m_player!=NULL && !m_player->GetAttributes()->IsActive()) )
	{
		m_player = GetTutorialPlayer()->GetRugbyCharacter();
	}

	// Give the ball to the player
	game->GetGameState()->SetBallHolder( m_player, true );

	LockAllGestures( m_player );

	// turn off formations
	SSEVDSFormationManager *formation_manager = game->GetTeam(0)->GetFormationManager();
	formation_manager->ForceClearFormation();

	// turn off formations
	SSEVDSFormationManager *formation_manager2 = game->GetTeam(1)->GetFormationManager();
	formation_manager2->ForceFormation("Tutorial",1);

	if ( !m_player->GetActionManager()->UFIsLocked( UF_DOMOTION ) )
		m_player->GetActionManager()->UFLock( UF_DOMOTION );

	// Prevent opposition team from moving
	SIFRugbyCharacterList opp_players = m_player->GetAttributes()->GetOppositionTeam()->GetPlayers();
	for ( SIFRugbyCharacterList::const_iterator iter = opp_players.begin(); iter != opp_players.end(); ++iter )
	{
		if ( !(*iter)->GetActionManager()->UFIsLocked( UF_DOMOTION ) )
			(*iter)->GetActionManager()->UFLock( UF_DOMOTION );
	}

	// Prevent any players on our team from doing kicks
	SIFRugbyCharacterList team_players = m_player->GetAttributes()->GetTeam()->GetPlayers();
	for ( SIFRugbyCharacterList::const_iterator iter = team_players.begin(); iter != team_players.end(); ++iter )
	{
		if ( (*iter) != m_player && !(*iter)->GetActionManager()->HFIsLocked( HF_KICK ) )
			(*iter)->GetActionManager()->HFLock( HF_KICK );
	}

	// m_player->GetHumanPlayer()->SetInputIgnored( RU_KICK_PUNT );
	// m_player->GetActionManager()->HFLock( HF_KICK );

	// Initialise win conditions
	SetSuccessState( false, true );
	SetFailureState( false, true );
	kick_started = false;
	ball_passed = false;
}

void RUTutorialKickingCancel::Finish()
{
	if (m_player && m_player->GetAttributes())
	{
		// Fix up locks on opponents
		if (m_player->GetAttributes()->GetOppositionTeam())
		{
			SIFRugbyCharacterList opp_players = m_player->GetAttributes()->GetOppositionTeam()->GetPlayers();
			for (SIFRugbyCharacterList::const_iterator iter = opp_players.begin(); iter != opp_players.end(); ++iter)
			{
				if ((*iter) && (*iter)->GetActionManager() && (*iter)->GetActionManager()->UFIsLocked(UF_DOMOTION))
				{
					(*iter)->GetActionManager()->UFUnlock(UF_DOMOTION);
				}
			}
		}

		// Fix up locks on our team
		if (m_player->GetAttributes()->GetTeam())
		{
			SIFRugbyCharacterList team_players = m_player->GetAttributes()->GetTeam()->GetPlayers();
			for (SIFRugbyCharacterList::const_iterator iter = team_players.begin(); iter != team_players.end(); ++iter)
			{
				if ((*iter) && (*iter) != m_player && (*iter)->GetActionManager() && (*iter)->GetActionManager()->HFIsLocked(HF_KICK))
				{
					(*iter)->GetActionManager()->HFUnlock(HF_KICK);
				}
			}
		}

		if (m_player->GetActionManager() && m_player->GetActionManager()->UFIsLocked(UF_DOMOTION))
		{
			m_player->GetActionManager()->UFUnlock(UF_DOMOTION);
		}
	}

	m_player = NULL;
}

// CleanUp the tutorial
void RUTutorialKickingCancel::CleanUp()
{
#ifdef ENABLE_ANALYTICS
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkTutorialViewed(wwAnalyticsTutorialOption::CancelKicking);
	}
#endif

	// Turn rules back on
	game->GetRules()->EnableConsequences( true );

	// Cleanup event listeners and anything else relevant.
	RUGameEvents* game_events = game->GetEvents();
	game_events->ball_dead_detected.Remove( this, &RUTutorialKickingCancel::BallInTouch );
	game_events->ball_out_detected.Remove( this, &RUTutorialKickingCancel::BallInTouch );
	game_events->ball_bounce.Remove( this, &RUTutorialKickingCancel::BallBounce );
	game_events->pass.Remove( this, &RUTutorialKickingCancel::OnPass );
	game_events->ball_holder_changed.Remove( this, &RUTutorialKickingCancel::OnBallHolderChanged );
	game_events->tackle.Remove( this, &RUTutorialKickingCancel::OnTackle );

	m_player = NULL;
}

// Has user kicked the ball out (not on the full)?
bool RUTutorialKickingCancel::HasSucceeded()
{
	return GetSuccessState() && !GetFailureState();
}

// Has user gone in the wrong direction or not moved for Y seconds?
bool RUTutorialKickingCancel::HasFailed()
{
	return GetFailureState();
}

void RUTutorialKickingCancel::BallBounce( const FVector& /*position*/, const FVector& /*velocity*/)
{
	mFailString = "[ID_TUTORIAL_KICKING_CANCEL_FAIL_01]";
	SetFailureState( true );
}

void RUTutorialKickingCancel::BallInTouch(ARugbyCharacter* /*ball_holder*/, const FVector& /*position*/, bool /*on_full*/)
{
	mFailString = "[ID_TUTORIAL_KICKING_CANCEL_FAIL_01]";
	SetFailureState( true );
}

bool RUTutorialKickingCancel::IsStageComplete( RUStageNum stage_num )
{
	if ( HasFailed() )
		return false;

	switch ( stage_num )
	{
	case RUStageNum::ONE:
		{
			if ( GetTutorialPlayer()->IsPressed( ERugbyGameAction::KICK_PUNT ) )
			{
				kick_started = true;

				return true;
			}
		}
		break;
	case RUStageNum::TWO:
		{
			if ( ball_passed )
				return true;
		}
	default: break;
	}

	return false;
}

void RUTutorialKickingCancel::CalculateMedal( )
{
	if ( mMedalAchievement < RUTutorialMedal::GOLD )
	{
		game->GetTutorialManager()->DisplayAchievement( RUTutorialMedal::GOLD );
		mMedalAchievement = RUTutorialMedal::GOLD;
	}
	mSuccessString = "[ID_TUTORIAL_KICKING_CANCEL_SUCCESS_01]";
}


void RUTutorialKickingCancel::OnPass( ARugbyCharacter*, ARugbyCharacter*, const FVector&, PASS_TYPE, bool )
{
	if ( !kick_started )
	{
		mFailString = "[ID_TUTORIAL_KICKING_CANCEL_FAIL_01]";
		SetFailureState( true );
	}

	ball_passed = true;
}

void RUTutorialKickingCancel::OnBallHolderChanged( ARugbyCharacter* new_holder, ARugbyCharacter* )
{
	if ( new_holder != NULL && m_player != NULL && new_holder->GetAttributes()->GetTeam() != m_player->GetAttributes()->GetTeam() )
	{
		mFailString = "[ID_TUTORIAL_KICKING_CANCEL_FAIL_01]";
		SetFailureState( true );
		return;
	}

	if ( kick_started && ball_passed )
	{
		SetSuccessState( true );
		if ( !mDemoActive )
			CalculateMedal();
	}
}

void RUTutorialKickingCancel::OnTackle( const RUTackleResult& )
{
	if ( game->GetGameState()->GetBallHolder() == m_player )
	{
		mFailString = "[ID_TUTORIAL_KICKING_CANCEL_FAIL_02]";
		SetFailureState( true );
	}
}

/*--------------------------------------------------------------
|        Copyright (C) 1997-2011 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

//#rc3_legacy_pch #include "Precompiled.h"
#include "Match/RugbyUnion/TutorialMode/Tutorials/RUTutorialLooseBall.h"

#include "Match/AI/Actions/RUAction.h"
#include "Match/AI/Actions/RUActionTacklee.h"
#include "Match/AI/Formations/SSEVDSFormationManager.h"
#include "Match/AI/Roles/Competitors/RURoleGetTheBall.h"
#include "Match/Ball/SSBall.h"
#include "Match/Components/RUActionManager.h"
#include "Match/Components/RUPlayerAttributes.h"
#include "Match/Components/RUPlayerMovement.h"
#include "Match/Components/SSHumanPlayer.h"
#include "Character/RugbyPlayerController.h"
#include "Match/HUD/RU3DHUDManager.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/RugbyUnion/RUStrategyHelper.h"
#include "Match/RugbyUnion/RUTeam.h"
#include "Match/RugbyUnion/TutorialMode/RUTutorialManager.h"
#include "Match/SIFGameObject.h"
#include "Match/SIFGameWorld.h"
#include "Match/SSRoleNull.h"
#include "Match/SSSpatialHelper.h"

//#rc3_legacy_include #include "RUContextualHelper.h"

namespace RUTUTORIALLOOSEBALL
{
	// Medal distances
	//RussellD unused? static const float GOLD_DIST = 30.0f;
	//RussellD unused? static const float SILVER_DIST = 22.0f;
}

RUTutorialLooseBall::RUTutorialLooseBall(SIFGameWorld* game)
	: RUTutorial(game, RUTutorialType::LOOSE_BALL, RUTutorialGroup::LESSON1, "ruged/tutorials/loose_ball.json"),
opp_player( NULL ),
force_pickup( false ),
opp_force_pickup( false )
{
}

RUTutorialLooseBall::~RUTutorialLooseBall()
{

}

void RUTutorialLooseBall::Update(float /*delta_time*/)
{
	MABASSERT(m_player);

	if ( !m_player->GetMovement()->HasReachedWaypoint() )
	{
		if ( opp_player->GetActionManager()->UFIsLocked( UF_DOMOTION ) )
		{
			game->GetBall()->PropelBall(PI, 0.000001f, true);

			opp_player->GetActionManager()->UFUnlock( UF_DOMOTION );

			SSEVDSFormationManager *t1_formation_manager = game->GetTeam(0)->GetFormationManager();
			t1_formation_manager->ForceFormation("Tutorial",1);

			SSEVDSFormationManager *t2_formation_manager = game->GetTeam(1)->GetFormationManager();
			t2_formation_manager->ForceFormation("Tutorial",1);
		}
	}

	// Force pickup. Only force once or GTB goes crazy.
	if ( m_player->GetRole() && m_player->GetRole()->RTTGetType() == RURoleGetTheBall::RTTGetStaticType() && force_pickup )
	{
		RURoleGetTheBall* gtb_role = m_player->GetRole< RURoleGetTheBall >();
		gtb_role->ForceAction( GTB_ACTION::PICKUP );
		force_pickup = false;
	}

	if ( opp_player->GetRole() && opp_player->GetRole()->RTTGetType() == RURoleGetTheBall::RTTGetStaticType() && opp_force_pickup )
	{
		RURoleGetTheBall* gtb_role = opp_player->GetRole< RURoleGetTheBall >();
		gtb_role->ForceAction( GTB_ACTION::PICKUP );
		opp_force_pickup = false;
	}
}

bool RUTutorialLooseBall::HasSucceeded()
{
	if ( GetSuccessState() )
		return true;

	if ( game->GetGameState()->GetBallHolder() == m_player )
	{
		if ( !mDemoActive )
			CalculateMedal();

		SetSuccessState( true );
		return true;
	}

	return false;
}

bool RUTutorialLooseBall::HasFailed()
{
	if ( game->GetGameState()->GetBallHolder() == opp_player )
	{
		mFailString = "[ID_TUTORIAL_LOOSE_BALL_FAIL_01]";
		return true;
	}

	return false;
}

void RUTutorialLooseBall::Start()
{
	// turn off formations
	SSEVDSFormationManager *t1_formation_manager = game->GetTeam(0)->GetFormationManager();
	t1_formation_manager->ForceClearFormation();

	// turn off formations
	SSEVDSFormationManager *t2_formation_manager = game->GetTeam(1)->GetFormationManager();
	t2_formation_manager->ForceClearFormation();

	MabString name(32, "ID_TUTORIAL_LOOSE_BALL");
	game->GetTutorialManager()->DisplayObjective( name );

	game->GetGameState()->SetBallHolder( NULL );
	game->GetBall()->SetRotation( MabQuaternion::IDENTITY );
	game->GetBall()->SetPositionAbsolute( FVector(0,0.05f, 19.5f) );

	m_player = GetTutorialPlayer()->GetRugbyCharacter();
	MABASSERT(m_player);
	opp_player = GetTutorialPlayer()->GetTeam()->GetOppositionTeam()->GetPlayer(0);
	MABASSERT(opp_player);
	RUActionManager* action_manager = opp_player->GetActionManager();
	MABASSERT( action_manager );

	if ( !action_manager->UFIsLocked( UF_DOMOTION ) )
		action_manager->UFLock( UF_DOMOTION );

	SIFRugbyCharacterList players = game->GetPlayers();
	for ( unsigned int i = 0; i < players.size(); ++i )
	{
		// Make sure players are all facing forwards!
		players[i]->GetMovement()->SetCurrentFacingAngle( m_player->GetAttributes()->GetTeam()->GetPlayAngle() );
		players[i]->GetMovement()->SetFacingFlags(AFFLAG_FACETARGANGLE);
	}

	//tackle_action = action_manager->TempDeregisterAction( ACTION_TACKLER );
	action_manager->EnableAction( ACTION_TACKLER, false, true );
	action_manager->EnableAction( ACTION_BHRUN, false, true );

	game->GetStrategyHelper()->OnTutorialSetBallLoose( m_player );

	force_pickup = true;
	opp_force_pickup = true;

	SetSuccessState( false, true );
	SetFailureState( false, true );
}

void RUTutorialLooseBall::Initialise()
{
	MabVector<ERugbyGameAction> actions;
	actions.reserve(2);
	actions.push_back(ERugbyGameAction::ANALOG_L);
	actions.push_back(ERugbyGameAction::SPRINT);

	game->GetTutorialManager()->SetStage(RUStageNum::ONE, "[ID_TUTORIAL_LOOSE_BALL_STAGEONE]", "1", actions);
}

void RUTutorialLooseBall::Finish()
{
	if (opp_player)
	{
		RUActionManager* action_manager = opp_player->GetActionManager();
		MABASSERT(action_manager);
		if (action_manager)
		{
			if (action_manager->UFIsLocked(UF_DOMOTION))
			{
				action_manager->UFUnlock(UF_DOMOTION);
			}

			action_manager->EnableAction(ACTION_BHRUN, true, true);
			action_manager->EnableAction(ACTION_TACKLER, true, true);
			action_manager->EnableAllActions(true);
		}
	}
}

void RUTutorialLooseBall::CleanUp()
{
#ifdef ENABLE_ANALYTICS
	if (URugbyGameInstance* pRugbyGameInstance = SIFApplication::GetApplication())
	{
		pRugbyGameInstance->GetPlayerAnalyticsData().MarkTutorialViewed(wwAnalyticsTutorialOption::LooseBall);
	}
#endif

	opp_player = NULL;
	m_player = NULL;
}

void RUTutorialLooseBall::CalculateMedal()
{
	if ( mMedalAchievement < RUTutorialMedal::GOLD )
	{
		game->GetTutorialManager()->DisplayAchievement( RUTutorialMedal::GOLD );
		mMedalAchievement = RUTutorialMedal::GOLD;
	}
	mSuccessString = "[ID_TUTORIAL_LOOSE_BALL_SUCCESS_01]";
}
